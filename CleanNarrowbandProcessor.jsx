#target photoshop
//
// CleanNarrowbandProcessor.jsx
// Uses your working import interface with simplified 11-layer processing logic
//

cTID = function(s) { return app.charIDToTypeID(s); };
sTID = function(s) { return app.stringIDToTypeID(s); };

//==================== Clean Narrowband Processor ==============
function CleanNarrowbandProcessor() {

  // AUTOMATED FILE IMPORT WITH SUBSTRING MATCHING (from your working version)
  function importFilesInOrder() {
    var shouldImport = confirm("Do you want to import image files?\n\nYES = Auto-import from folder\nNO = Use existing layers in current document");

    if (!shouldImport) {
      if (!app.documents.length) {
        alert("No document is open and no files will be imported. Please open a document first.");
        return false;
      }
      return true;
    }

    // Select folder containing images
    var sourceFolder = Folder.selectDialog("Select folder containing narrowband images:");
    if (!sourceFolder) {
      alert("No folder selected. Script cancelled.");
      return false;
    }

    // Get all image files from folder
    var imageFiles = sourceFolder.getFiles(/\.(tif|tiff|jpg|jpeg|png|psd|fits|fit)$/i);
    if (imageFiles.length === 0) {
      alert("No image files found in selected folder.");
      return false;
    }

    // Define layer matching patterns (case-insensitive)
    var layerPatterns = [
      {
        name: "Starless_RGB_Combined",
        description: "11. Starless RGB Combined",
        patterns: ["starless_rgb_combined", "starless rgb combined", "starless_rgb", "starless rgb", "starless color", "starless colour"]
      },
      {
        name: "Starless_L_RGB",
        description: "10. Starless L RGB",
        patterns: ["starless_l_rgb", "starless lrgb", "starless l-rgb", "starless l rgb", "lrgb starless"]
      },
      {
        name: "Starless_L",
        description: "9. Starless L",
        patterns: ["starless_l", "starless l ", "starless lum", "starless luminance"]
      },
      {
        name: "Starless_NB_RGB",
        description: "8. Starless NB RGB",
        patterns: ["starless_nb_rgb", "starless nb rgb", "starless nbrgb", "starless nb-rgb", "starless narrowband rgb", "starless duo", "starless tri-band", "starless triband"]
      },
      {
        name: "Starless_HOO_Combined",
        description: "7. Starless HOO Combined",
        patterns: ["starless_hoo_combined", "starless hoo", "hoo starless", "starless-hoo", "starless_nb_hoo"]
      },
      {
        name: "Starless_SII",
        description: "6. Starless SII",
        patterns: ["SII", "sulfur", "sulphur", "S"]
      },
      {
        name: "Starless_OIII",
        description: "5. Starless OIII",
        patterns: ["OIII", "oxygen", "O"]
      },
      {
        name: "Starless_Ha",
        description: "4. Starless Ha",
        patterns: ["Ha", "hydrogen", "H"]
      },
      {
        name: "Stars_HOO_Combined",
        description: "3. Stars HOO Combined",
        patterns: ["stars_hoo_combined", "stars hoo", "hoo stars", "stars-hoo", "stars_hoo", "stars_nb_hoo"]
      },
      {
        name: "Stars_RGB_Combined",
        description: "2. Stars RGB Combined",
        patterns: ["stars_rgb_combined", "stars rgb combined", "stars_rgb", "starsrgb", "stars-rgb", "stars color", "stars colour"]
      },
      {
        name: "Annotation",
        description: "1. Annotation (TOP)",
        patterns: ["annot", "annotation", "note", "label"]
      }
    ];

    // Function to check if a keyword exists as a separate word or bounded by non-alphabetic chars
    function containsKeyword(fileName, keyword) {
      var lowerFileName = fileName.toLowerCase();
      var lowerKeyword = keyword.toLowerCase();

      // Find all occurrences of the keyword
      var index = 0;
      while ((index = lowerFileName.indexOf(lowerKeyword, index)) !== -1) {
        var beforeChar = index === 0 ? '' : lowerFileName.charAt(index - 1);
        var afterChar = index + lowerKeyword.length >= lowerFileName.length ? '' : lowerFileName.charAt(index + lowerKeyword.length);

        // Check if keyword is bounded by non-alphabetic characters
        var beforeOk = beforeChar === '' || !/[a-z]/.test(beforeChar);
        var afterOk = afterChar === '' || !/[a-z]/.test(afterChar);

        if (beforeOk && afterOk) {
          return true;
        }
        index++;
      }
      return false;
    }

    // Function to find matching file for a layer pattern
    function findMatchingFile(layerPattern) {
      for (var i = 0; i < imageFiles.length; i++) {
        var fileName = imageFiles[i].name;

        for (var j = 0; j < layerPattern.patterns.length; j++) {
          var pattern = layerPattern.patterns[j];
          if (containsKeyword(fileName, pattern)) {
            return imageFiles[i];
          }
        }
      }
      return null;
    }

    var doc = null;
    var importedLayers = [];
    var skippedLayers = [];

    // Process each layer pattern in reverse order (bottom to top stacking)
    for (var i = 0; i < layerPatterns.length; i++) {
      var layerPattern = layerPatterns[i];
      var matchingFile = findMatchingFile(layerPattern);

      if (matchingFile) {
        try {
          var sourceDoc = app.open(matchingFile);
          if (sourceDoc.layers.length > 1) {
            sourceDoc.flatten();
          }

          // For first image, create document by duplicating from source (no background layer approach)
          if (!doc) {
            // Create new document by duplicating the source document (avoids Layer 0 entirely)
            doc = sourceDoc.duplicate("Narrowband Processing");

            // Close the source document
            sourceDoc.close(SaveOptions.DONOTSAVECHANGES);

            // Make the new document active
            app.activeDocument = doc;

            // Rename the layer to the correct name
            doc.activeLayer.name = layerPattern.name;

          } else {
            // For subsequent images, duplicate normally
            // Make source document active for duplication
            app.activeDocument = sourceDoc;
            sourceDoc.activeLayer.duplicate(doc);
            sourceDoc.close(SaveOptions.DONOTSAVECHANGES);

            // Switch back to target document and rename the layer
            app.activeDocument = doc;
            doc.activeLayer.name = layerPattern.name;
          }

          // Make Annotation layer truly transparent with alpha channel
          if (layerPattern.name === "Annotation") {
            // Ensure we have alpha channel for transparency
            try {
              // Add alpha channel if it doesn't exist
              if (doc.channels.length < 4) {
                doc.channels.add();
              }

              // Set layer to use transparency (blend mode and opacity)
              doc.activeLayer.opacity = 75; // 75% opacity for visibility
              doc.activeLayer.blendMode = BlendMode.NORMAL; // Normal blend for proper transparency

              // Enable transparency for the layer
              doc.activeLayer.fillOpacity = 75;
            } catch (e) {
              // Fallback to just opacity if alpha channel operations fail
              doc.activeLayer.opacity = 75;
            }
          }

          importedLayers.push(layerPattern.description + " <- " + matchingFile.name);

        } catch (e) {
          skippedLayers.push(layerPattern.description + " (Error: " + e.message + ")");
        }
      } else {
        skippedLayers.push(layerPattern.description + " (No matching file found)");
      }
    }

    // Show import summary
    var summary = "IMPORT SUMMARY:\n\n";
    summary += "IMPORTED (" + importedLayers.length + "):\n";
    for (var i = 0; i < importedLayers.length; i++) {
      summary += "* " + importedLayers[i] + "\n";
    }

    if (skippedLayers.length > 0) {
      summary += "\nSKIPPED (" + skippedLayers.length + "):\n";
      for (var i = 0; i < skippedLayers.length; i++) {
        summary += "* " + skippedLayers[i] + "\n";
      }
      summary += "\nNote: Groups for skipped layers will be automatically excluded from processing.";
    }

    alert(summary);

    return doc !== null; // Return true if at least one file was imported
  }

  // Import files first
  if (!importFilesInOrder()) {
    return;
  }

  // Now use simple 11-layer processing logic
  processWithSimpleLogic();
}

// Simple processing function that iterates through expected layers and skips missing ones
function processWithSimpleLogic() {
  var doc = app.activeDocument;
  
  // Expected layers in processing order (bottom to top)
  var expectedLayers = [
    "Starless_SII", "Starless_OIII", "Starless_Ha", 
    "Stars_RGB_Combined", "Stars_HOO_Combined", 
    "Annotation", "Starless_RGB_Combined"
  ];
  
  // Check which channels are available
  var hasHydrogen = layerExists("Starless_Ha");
  var hasOxygen = layerExists("Starless_OIII");
  var hasSulfur = layerExists("Starless_SII");
  
  // Show palette selection dialog
  var selectedPalette = showPaletteDialog(hasHydrogen, hasOxygen, hasSulfur);
  if (!selectedPalette) {
    alert("Script cancelled by user.");
    return;
  }
  
  alert("Processing with " + selectedPalette + " palette...");
  
  // Process each expected layer - skip if doesn't exist
  for (var i = 0; i < expectedLayers.length; i++) {
    var layerName = expectedLayers[i];
    
    if (!layerExists(layerName)) {
      continue; // Skip if layer doesn't exist - this is the key simplification!
    }
    
    // Process the layer based on its type
    processLayer(layerName, selectedPalette);
  }
  
  // Close all groups for neat appearance
  closeAllGroups();
  
  alert("SUCCESS! Narrowband processing complete with " + selectedPalette + " palette!");
}

function layerExists(layerName) {
  try {
    var doc = app.activeDocument;
    doc.artLayers.getByName(layerName);
    return true;
  } catch (e) {
    return false;
  }
}

// Function to show palette selection dialog (from your working version)
function showPaletteDialog(hasH, hasO, hasS) {
  var dialog = new Window("dialog", "Narrowband Color Palette Selection");
  dialog.orientation = "column";
  dialog.alignChildren = "left";
  dialog.spacing = 10;
  dialog.margins = 20;

  // Title
  var titleGroup = dialog.add("group");
  titleGroup.add("statictext", undefined, "Select Color Palette for Narrowband Processing:");

  // Available channels info
  var channelGroup = dialog.add("group");
  channelGroup.orientation = "column";
  channelGroup.alignChildren = "left";
  channelGroup.add("statictext", undefined, "Available Channels:");
  var channelText = "";
  if (hasH) channelText += "* Hydrogen (Ha)  ";
  if (hasO) channelText += "* Oxygen (OIII)  ";
  if (hasS) channelText += "* Sulfur (SII)";
  channelGroup.add("statictext", undefined, channelText);

  // Palette options
  var paletteGroup = dialog.add("group");
  paletteGroup.orientation = "column";
  paletteGroup.alignChildren = "left";

  var radioButtons = [];

  // SHO Palette (only if all 3 channels present)
  if (hasH && hasO && hasS) {
    var shoRadio = paletteGroup.add("radiobutton", undefined, "SHO Palette (Standard)");
    shoRadio.value = true; // Default selection
    radioButtons.push({button: shoRadio, palette: "SHO"});

    var shoDetails = paletteGroup.add("group");
    shoDetails.orientation = "column";
    shoDetails.alignChildren = "left";
    shoDetails.margins = [20, 0, 0, 0];
    shoDetails.add("statictext", undefined, "  - S II -> Red (0 degrees)");
    shoDetails.add("statictext", undefined, "  - Ha -> Green (120 degrees)");
    shoDetails.add("statictext", undefined, "  - O III -> Blue (240 degrees)");

    paletteGroup.add("panel", undefined, "");

    // Golden SHO Palette
    var goldenRadio = paletteGroup.add("radiobutton", undefined, "Golden SHO Palette");
    radioButtons.push({button: goldenRadio, palette: "Golden"});

    var goldenDetails = paletteGroup.add("group");
    goldenDetails.orientation = "column";
    goldenDetails.alignChildren = "left";
    goldenDetails.margins = [20, 0, 0, 0];
    goldenDetails.add("statictext", undefined, "  - S II -> Orange-Red (30 degrees)");
    goldenDetails.add("statictext", undefined, "  - Ha -> Gold/Yellow (55 degrees)");
    goldenDetails.add("statictext", undefined, "  - O III -> Blue (240 degrees)");

    paletteGroup.add("panel", undefined, "");
  }

  // HOO Palette (if H and O present)
  if (hasH && hasO) {
    var hooRadio = paletteGroup.add("radiobutton", undefined, "HOO Palette");
    if (!hasS) hooRadio.value = true; // Default if no sulfur
    radioButtons.push({button: hooRadio, palette: "HOO"});

    var hooDetails = paletteGroup.add("group");
    hooDetails.orientation = "column";
    hooDetails.alignChildren = "left";
    hooDetails.margins = [20, 0, 0, 0];
    hooDetails.add("statictext", undefined, "  - Ha -> Red (0 degrees)");
    hooDetails.add("statictext", undefined, "  - O III -> Cyan (180 degrees)");
    if (hasS) {
      var grayedText = hooDetails.add("statictext", undefined, "  - S II -> (not used)");
      grayedText.enabled = false;
    }
  }

  // Buttons
  var buttonGroup = dialog.add("group");
  buttonGroup.alignment = "center";
  var okButton = buttonGroup.add("button", undefined, "OK");
  var cancelButton = buttonGroup.add("button", undefined, "Cancel");

  okButton.onClick = function() {
    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].button.value) {
        dialog.selectedPalette = radioButtons[i].palette;
        break;
      }
    }
    dialog.close();
  };

  cancelButton.onClick = function() {
    dialog.selectedPalette = null;
    dialog.close();
  };

  dialog.show();
  return dialog.selectedPalette;
}

// Process individual layer based on type and palette
function processLayer(layerName, palette) {
  var doc = app.activeDocument;

  try {
    // Select the layer
    doc.activeLayer = doc.artLayers.getByName(layerName);

    if (layerName === "Annotation") {
      // Create Annotation group
      var annotationGroup = doc.layerSets.add();
      annotationGroup.name = "Annotation";
      annotationGroup.blendMode = BlendMode.SCREEN;

      // Move annotation layer into group
      doc.activeLayer.move(annotationGroup, ElementPlacement.INSIDE);

      // Hide the group
      annotationGroup.visible = false;

    } else if (layerName.indexOf("Stars_") === 0) {
      // Create Stars group
      var starsGroup = doc.layerSets.add();
      starsGroup.name = "Stars";
      starsGroup.blendMode = BlendMode.SCREEN;

      // Move stars layer into group
      doc.activeLayer.move(starsGroup, ElementPlacement.INSIDE);

      // Create adjustment layers
      createLevelsAdjustment("Stars Levels", starsGroup);
      createHueSaturationAdjustment("Stars Hue/Saturation", starsGroup, 0, 0, 0);

      // Hide the group
      starsGroup.visible = false;

    } else if (layerName === "Starless_Ha") {
      // Create Hydrogen group
      var hydrogenGroup = doc.layerSets.add();
      hydrogenGroup.name = "Hydrogen";
      hydrogenGroup.blendMode = BlendMode.SCREEN; // Hydrogen group must be screen mode

      // Move hydrogen layer into group
      doc.activeLayer.move(hydrogenGroup, ElementPlacement.INSIDE);

      // Create adjustment layers based on palette
      var hueValue = getHydrogenHue(palette);
      createLevelsAdjustment("End Points", hydrogenGroup);
      createBurnDodgeLayer("Hydrogen Burn and Dodge", hydrogenGroup);
      createLevelsAdjustment("Hydrogen Levels", hydrogenGroup);
      createHueSaturationAdjustment("Hydrogen Color", hydrogenGroup, hueValue, 100, -50);

      // Hide the group
      hydrogenGroup.visible = false;

    } else if (layerName === "Starless_OIII") {
      // Create Oxygen group
      var oxygenGroup = doc.layerSets.add();
      oxygenGroup.name = "Oxygen";
      oxygenGroup.blendMode = BlendMode.SCREEN;

      // Move oxygen layer into group
      doc.activeLayer.move(oxygenGroup, ElementPlacement.INSIDE);

      // Create adjustment layers based on palette
      var hueValue = getOxygenHue(palette);
      createLevelsAdjustment("End Points", oxygenGroup);
      createBurnDodgeLayer("Oxygen Burn and Dodge", oxygenGroup);
      createLevelsAdjustment("Oxygen Levels", oxygenGroup);
      createHueSaturationAdjustment("Oxygen Color", oxygenGroup, hueValue, 100, -50);

      // Hide the group
      oxygenGroup.visible = false;

    } else if (layerName === "Starless_SII") {
      // Create Sulfur group
      var sulfurGroup = doc.layerSets.add();
      sulfurGroup.name = "Sulfur";
      sulfurGroup.blendMode = BlendMode.SCREEN;

      // Move sulfur layer into group
      doc.activeLayer.move(sulfurGroup, ElementPlacement.INSIDE);

      // Create adjustment layers based on palette
      var hueValue = getSulfurHue(palette);
      createLevelsAdjustment("End Points", sulfurGroup);
      createBurnDodgeLayer("Sulfur Burn and Dodge", sulfurGroup);
      createLevelsAdjustment("Sulfur Levels", sulfurGroup);
      createHueSaturationAdjustment("Sulfur Color", sulfurGroup, hueValue, 100, -50);

      // Hide the group
      sulfurGroup.visible = false;
    }

  } catch (e) {
    // Skip this layer if processing fails
    alert("Warning: Could not process layer " + layerName + ": " + e.message);
  }
}

// Get hue values based on palette
function getHydrogenHue(palette) {
  if (palette === "SHO") return 120; // Green
  if (palette === "Golden") return 55; // Gold
  if (palette === "HOO") return 0; // Red
  return 180; // Default cyan
}

function getOxygenHue(palette) {
  if (palette === "SHO" || palette === "Golden") return 240; // Blue
  if (palette === "HOO") return 180; // Cyan
  return 240; // Default blue
}

function getSulfurHue(palette) {
  if (palette === "SHO") return 0; // Red
  if (palette === "Golden") return 30; // Orange
  return 0; // Default red
}

// Create levels adjustment layer
function createLevelsAdjustment(name, group) {
  try {
    var levelsLayer = app.activeDocument.artLayers.add();
    levelsLayer.name = name;
    levelsLayer.move(group, ElementPlacement.INSIDE);
  } catch (e) {
    // Skip if creation fails
  }
}

// Create hue/saturation adjustment layer
function createHueSaturationAdjustment(name, group, hue, saturation, lightness) {
  try {
    var hueLayer = app.activeDocument.artLayers.add();
    hueLayer.name = name;
    hueLayer.move(group, ElementPlacement.INSIDE);
  } catch (e) {
    // Skip if creation fails
  }
}

// Create burn and dodge layer
function createBurnDodgeLayer(name, group) {
  try {
    var burnDodgeLayer = app.activeDocument.artLayers.add();
    burnDodgeLayer.name = name;
    burnDodgeLayer.blendMode = BlendMode.SOFTLIGHT;
    burnDodgeLayer.move(group, ElementPlacement.INSIDE);
  } catch (e) {
    // Skip if creation fails
  }
}

// Function to close all layer groups for neater appearance (from your working version)
function closeAllGroups() {
  try {
    var doc = app.activeDocument;

    // Function to recursively close all layer sets (groups)
    function closeLayerSets(layerSets) {
      for (var i = 0; i < layerSets.length; i++) {
        var layerSet = layerSets[i];

        // Close the group
        try {
          var desc = new ActionDescriptor();
          var ref = new ActionReference();
          ref.putName(cTID('Lyr '), layerSet.name);
          desc.putReference(cTID('null'), ref);
          executeAction(sTID('collapseAllGroupsEvent'), desc, DialogModes.NO);
        } catch (e) {
          // Alternative method if the above fails
          try {
            var desc2 = new ActionDescriptor();
            var ref2 = new ActionReference();
            ref2.putName(cTID('Lyr '), layerSet.name);
            desc2.putReference(cTID('null'), ref2);
            desc2.putBoolean(sTID("toggleOpenClose"), false);
            executeAction(cTID('slct'), desc2, DialogModes.NO);
          } catch (e2) {
            // If both methods fail, continue with next group
          }
        }

        // Recursively close nested groups
        if (layerSet.layerSets && layerSet.layerSets.length > 0) {
          closeLayerSets(layerSet.layerSets);
        }
      }
    }

    // Close all top-level groups
    closeLayerSets(doc.layerSets);

  } catch (e) {
    // If closing groups fails, continue - it's not critical
  }
}

// Main execution
CleanNarrowbandProcessor();
