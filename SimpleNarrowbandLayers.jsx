#target photoshop
//
// SimpleNarrowbandLayers.jsx
// Simple approach - iterate through expected 11 layers and skip if not found
//

cTID = function(s) { return app.charIDToTypeID(s); };
sTID = function(s) { return app.stringIDToTypeID(s); };

// Search patterns for layer detection (case-insensitive substrings)
var layerPatterns = {
    annotation: ["annot"],
    starsRGB: ["stars_rgb_combined", "stars_rgb", "stars"],
    starsHOO: ["stars_hoo_combined", "stars_hoo"],
    starsSHO: ["stars_sho_combined", "stars_sho"],
    starsGoldenSHO: ["stars_golden_sho_combined", "stars_golden_sho"],
    hydrogen: ["starless_ha", "starless_h", "ha", "h"],
    oxygen: ["starless_oiii", "starless_o", "oiii", "o"],
    sulfur: ["starless_sii", "starless_s", "sii", "s"],
    starlessRGB: ["starless_rgb_combined", "starless_rgb"],
    rgbCombined: ["rgb_combined", "rgb"]
};

// Expected layer processing order (bottom to top)
var processingOrder = [
    "sulfur", "oxygen", "hydrogen", "starsRGB", "starsHOO",
    "starsSHO", "starsGoldenSHO", "annotation", "starlessRGB", "rgbCombined"
];

// Palette configurations
var palettes = {
    SHO: { S: 0, H: 120, O: 240 },      // S=Red, H=Green, O=Blue
    Golden_SHO: { S: 30, H: 55, O: 240 }, // S=Orange, H=Gold, O=Blue  
    HOO: { H: 0, O: 180 }                // H=Red, O=Cyan
};

function findLayerByPatterns(patterns) {
    // Search through all layers for matching patterns
    var doc = app.activeDocument;
    for (var i = 0; i < doc.layers.length; i++) {
        var layerName = doc.layers[i].name.toLowerCase();
        for (var j = 0; j < patterns.length; j++) {
            if (layerName.indexOf(patterns[j].toLowerCase()) !== -1) {
                return doc.layers[i].name; // Return actual layer name
            }
        }
    }
    return null;
}

function layerExists(layerName) {
    try {
        var ref = new ActionReference();
        ref.putName(cTID('Lyr '), layerName);
        var desc = executeActionGet(ref);
        return true;
    } catch (e) {
        return false;
    }
}

function selectLayer(layerName) {
    try {
        var desc = new ActionDescriptor();
        var ref = new ActionReference();
        ref.putName(cTID('Lyr '), layerName);
        desc.putReference(cTID('null'), ref);
        desc.putBoolean(cTID('MkVs'), false);
        executeAction(cTID('slct'), desc, DialogModes.NO);
        return true;
    } catch (e) {
        return false;
    }
}

function createGroup(groupName) {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putClass(sTID("layerSection"));
    desc.putReference(cTID('null'), ref);
    desc.putString(cTID('Nm  '), groupName);
    executeAction(cTID('Mk  '), desc, DialogModes.NO);
}

function setLayerName(name) {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
    desc.putReference(cTID('null'), ref);
    var desc2 = new ActionDescriptor();
    desc2.putString(cTID('Nm  '), name);
    desc.putObject(cTID('T   '), cTID('Lyr '), desc2);
    executeAction(cTID('setd'), desc, DialogModes.NO);
}

function setBlendMode(blendMode) {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
    desc.putReference(cTID('null'), ref);
    var desc2 = new ActionDescriptor();
    desc2.putEnumerated(cTID('Md  '), cTID('BlnM'), blendMode);
    desc.putObject(cTID('T   '), cTID('Lyr '), desc2);
    executeAction(cTID('setd'), desc, DialogModes.NO);
}

function hideLayer() {
    var desc = new ActionDescriptor();
    var list = new ActionList();
    var ref = new ActionReference();
    ref.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
    list.putReference(ref);
    desc.putList(cTID('null'), list);
    executeAction(cTID('Hd  '), desc, DialogModes.NO);
}

function createLevelsAdjustment(name) {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putClass(cTID('AdjL'));
    desc.putReference(cTID('null'), ref);
    var desc2 = new ActionDescriptor();
    var desc3 = new ActionDescriptor();
    desc2.putObject(cTID('Type'), cTID('Lvls'), desc3);
    desc.putObject(cTID('Usng'), cTID('AdjL'), desc2);
    executeAction(cTID('Mk  '), desc, DialogModes.NO);
    setLayerName(name);
}

function createHueSaturationAdjustment(name, hue, saturation, lightness) {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putClass(cTID('AdjL'));
    desc.putReference(cTID('null'), ref);
    var desc2 = new ActionDescriptor();
    var desc3 = new ActionDescriptor();
    desc2.putObject(cTID('Type'), cTID('HStr'), desc3);
    desc.putObject(cTID('Usng'), cTID('AdjL'), desc2);
    executeAction(cTID('Mk  '), desc, DialogModes.NO);
    
    // Set hue/saturation values
    var desc4 = new ActionDescriptor();
    var ref2 = new ActionReference();
    ref2.putEnumerated(cTID('AdjL'), cTID('Ordn'), cTID('Trgt'));
    desc4.putReference(cTID('null'), ref2);
    var desc5 = new ActionDescriptor();
    desc5.putEnumerated(sTID("presetKind"), sTID("presetKindType"), sTID("presetKindCustom"));
    desc5.putBoolean(cTID('Clrz'), true);
    var list = new ActionList();
    var desc6 = new ActionDescriptor();
    desc6.putEnumerated(cTID('Chnl'), cTID('Chnl'), cTID('Cmps'));
    desc6.putInteger(cTID('H   '), hue);
    desc6.putInteger(cTID('Strt'), saturation);
    desc6.putInteger(cTID('Lght'), lightness);
    list.putObject(cTID('Hst2'), desc6);
    desc5.putList(cTID('Adjs'), list);
    desc4.putObject(cTID('T   '), cTID('HStr'), desc5);
    executeAction(cTID('setd'), desc4, DialogModes.NO);
    
    setLayerName(name);
}

function createBurnDodgeLayer(name) {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putClass(cTID('Lyr '));
    desc.putReference(cTID('null'), ref);
    executeAction(cTID('Mk  '), desc, DialogModes.NO);
    setBlendMode(cTID('SftL')); // Soft Light
    setLayerName(name);
}

function closeAllGroups() {
    try {
        var desc = new ActionDescriptor();
        desc.putInteger(sTID("layerSectionStart"), 1);
        desc.putInteger(sTID("layerSectionEnd"), 2);
        executeAction(sTID("collapseAllGroupsEvent"), desc, DialogModes.NO);
    } catch (e) {
        // Ignore errors - this is just for cleanup
    }
}

function showPaletteDialog(hasS, hasH, hasO) {
    var dialog = new Window("dialog", "Select Narrowband Palette");
    dialog.orientation = "column";
    dialog.alignChildren = "left";

    var paletteGroup = dialog.add("panel", undefined, "Available Palettes");
    paletteGroup.orientation = "column";
    paletteGroup.alignChildren = "left";

    var selectedPalette = null;
    var radioButtons = [];

    // SHO palette (only if S, H, O all present)
    if (hasS && hasH && hasO) {
        var shoRadio = paletteGroup.add("radiobutton", undefined, "SHO (S=Red 0°, H=Green 120°, O=Blue 240°)");
        radioButtons.push({radio: shoRadio, palette: "SHO"});
        shoRadio.value = true; // Default selection
        selectedPalette = "SHO";
    }

    // Golden SHO palette (only if S, H, O all present)
    if (hasS && hasH && hasO) {
        var goldenRadio = paletteGroup.add("radiobutton", undefined, "Golden SHO (S=Orange 30°, H=Gold 55°, O=Blue 240°)");
        radioButtons.push({radio: goldenRadio, palette: "Golden_SHO"});
        if (!selectedPalette) {
            goldenRadio.value = true;
            selectedPalette = "Golden_SHO";
        }
    }

    // HOO palette (only if H, O present)
    if (hasH && hasO) {
        var hooRadio = paletteGroup.add("radiobutton", undefined, "HOO (H=Red 0°, O=Cyan 180°)");
        radioButtons.push({radio: hooRadio, palette: "HOO"});
        if (!selectedPalette) {
            hooRadio.value = true;
            selectedPalette = "HOO";
        }
    }

    // Set up radio button behavior
    for (var i = 0; i < radioButtons.length; i++) {
        radioButtons[i].radio.onClick = (function(palette) {
            return function() {
                selectedPalette = palette;
                for (var j = 0; j < radioButtons.length; j++) {
                    radioButtons[j].radio.value = (radioButtons[j].palette === palette);
                }
            };
        })(radioButtons[i].palette);
    }

    var buttonGroup = dialog.add("group");
    var okButton = buttonGroup.add("button", undefined, "OK");
    var cancelButton = buttonGroup.add("button", undefined, "Cancel");

    okButton.onClick = function() {
        dialog.close(1);
    };

    cancelButton.onClick = function() {
        dialog.close(0);
    };

    if (dialog.show() === 1) {
        return selectedPalette;
    }
    return null;
}

function processNarrowbandLayers() {
    // Find actual layer names using pattern matching
    var foundLayers = {};
    for (var layerType in layerPatterns) {
        var layerName = findLayerByPatterns(layerPatterns[layerType]);
        if (layerName) {
            foundLayers[layerType] = layerName;
        }
    }

    // Determine which channels are present
    var hasS = foundLayers.sulfur !== undefined;
    var hasH = foundLayers.hydrogen !== undefined;
    var hasO = foundLayers.oxygen !== undefined;
    var hasStarsRGB = foundLayers.starsRGB !== undefined;
    var hasAnnotation = foundLayers.annotation !== undefined;

    // Show palette selection dialog
    var selectedPalette = showPaletteDialog(hasS, hasH, hasO);
    if (!selectedPalette) {
        alert("Processing cancelled.");
        return;
    }

    var paletteConfig = palettes[selectedPalette];

    // Process layers in order
    for (var i = 0; i < processingOrder.length; i++) {
        var layerType = processingOrder[i];
        var layerName = foundLayers[layerType];

        if (!layerName || !selectLayer(layerName)) {
            continue; // Skip if layer doesn't exist or can't select
        }

        // Process based on layer type
        if (layerType === "annotation") {
            createGroup("Annotation");
            setBlendMode(cTID('Scrn')); // Screen
            hideLayer();

        } else if (layerType.indexOf("stars") === 0) {
            createGroup("Stars");
            setBlendMode(cTID('Scrn')); // Screen
            createLevelsAdjustment("Stars Levels");
            createHueSaturationAdjustment("Stars Hue/Saturation", 0, 0, 0);
            hideLayer();

        } else if (layerType === "hydrogen") {
            createGroup("Hydrogen");
            setBlendMode(cTID('Scrn')); // Screen - Hydrogen group must be screen mode
            createLevelsAdjustment("End Points");
            createBurnDodgeLayer("Hydrogen Burn and Dodge");
            createLevelsAdjustment("Hydrogen Levels");
            var hHue = paletteConfig.H !== undefined ? paletteConfig.H : 180;
            createHueSaturationAdjustment("Hydrogen Color", hHue, 100, -50);
            hideLayer();

        } else if (layerType === "oxygen") {
            createGroup("Oxygen");
            setBlendMode(cTID('Scrn')); // Screen
            createLevelsAdjustment("End Points");
            createBurnDodgeLayer("Oxygen Burn and Dodge");
            createLevelsAdjustment("Oxygen Levels");
            var oHue = paletteConfig.O !== undefined ? paletteConfig.O : 240;
            createHueSaturationAdjustment("Oxygen Color", oHue, 100, -50);
            hideLayer();

        } else if (layerType === "sulfur") {
            createGroup("Sulfur");
            setBlendMode(cTID('Scrn')); // Screen
            createLevelsAdjustment("End Points");
            createBurnDodgeLayer("Sulfur Burn and Dodge");
            createLevelsAdjustment("Sulfur Levels");
            var sHue = paletteConfig.S !== undefined ? paletteConfig.S : 0;
            createHueSaturationAdjustment("Sulfur Color", sHue, 100, -50);
            hideLayer();
        }
    }

    // Create global adjustments group
    createGroup("Global Adjustments");
    createLevelsAdjustment("Global Levels");
    createBurnDodgeLayer("Global Burn and Dodge");
    hideLayer();

    // Close all groups for cleaner appearance
    closeAllGroups();

    alert("Narrowband layer processing complete with " + selectedPalette + " palette!");
}

// Main execution
processNarrowbandLayers();
