#target photoshop
//
// WorkingNarrowbandProcessor.jsx
// Restored working version with your import interface + simple processing
//

cTID = function(s) { return app.charIDToTypeID(s); };
sTID = function(s) { return app.stringIDToTypeID(s); };

//==================== Working Narrowband Processor ==============
function WorkingNarrowbandProcessor() {

  // AUTOMATED FILE IMPORT WITH SUBSTRING MATCHING (your working version)
  function importFilesInOrder() {
    var shouldImport = confirm("Do you want to import image files?\n\nYES = Auto-import from folder\nNO = Use existing layers in current document");

    if (!shouldImport) {
      if (!app.documents.length) {
        alert("No document is open and no files will be imported. Please open a document first.");
        return false;
      }
      return true;
    }

    // Select folder containing images
    var sourceFolder = Folder.selectDialog("Select folder containing narrowband images:");
    if (!sourceFolder) {
      alert("No folder selected. <PERSON>ript cancelled.");
      return false;
    }

    // Get all image files from folder
    var imageFiles = sourceFolder.getFiles(/\.(tif|tiff|jpg|jpeg|png|psd|fits|fit)$/i);
    if (imageFiles.length === 0) {
      alert("No image files found in selected folder.");
      return false;
    }

    // Define layer matching patterns (case-insensitive)
    var layerPatterns = [
      {
        name: "Starless_RGB_Combined",
        description: "11. Starless RGB Combined",
        patterns: ["starless_rgb_combined", "starless rgb combined", "starless_rgb", "starless rgb", "starless color", "starless colour"]
      },
      {
        name: "Starless_L_RGB",
        description: "10. Starless L RGB", 
        patterns: ["starless_l_rgb", "starless lrgb", "starless l-rgb", "starless l rgb", "lrgb starless"]
      },
      {
        name: "Starless_L",
        description: "9. Starless L",
        patterns: ["starless_l", "starless l ", "starless lum", "starless luminance"]
      },
      {
        name: "Starless_NB_RGB",
        description: "8. Starless NB RGB",
        patterns: ["starless_nb_rgb", "starless nb rgb", "starless nbrgb", "starless nb-rgb", "starless narrowband rgb", "starless duo", "starless tri-band", "starless triband"]
      },
      {
        name: "Starless_HOO_Combined",
        description: "7. Starless HOO Combined",
        patterns: ["starless_hoo_combined", "starless hoo", "hoo starless", "starless-hoo", "starless_nb_hoo"]
      },
      {
        name: "Starless_SII",
        description: "6. Starless SII",
        patterns: ["SII", "sulfur", "sulphur", "S"]
      },
      {
        name: "Starless_OIII",
        description: "5. Starless OIII",
        patterns: ["OIII", "oxygen", "O"]
      },
      {
        name: "Starless_Ha",
        description: "4. Starless Ha",
        patterns: ["Ha", "hydrogen", "H"]
      },
      {
        name: "Stars_HOO_Combined",
        description: "3. Stars HOO Combined",
        patterns: ["stars_hoo_combined", "stars hoo", "hoo stars", "stars-hoo", "stars_hoo", "stars_nb_hoo"]
      },
      {
        name: "Stars_RGB_Combined",
        description: "2. Stars RGB Combined",
        patterns: ["stars_rgb_combined", "stars rgb combined", "stars_rgb", "starsrgb", "stars-rgb", "stars color", "stars colour"]
      },
      {
        name: "Annotation",
        description: "1. Annotation (TOP)",
        patterns: ["annot", "annotation", "note", "label"]
      }
    ];

    // Function to check if a keyword exists as a separate word or bounded by non-alphabetic chars
    function containsKeyword(fileName, keyword) {
      var lowerFileName = fileName.toLowerCase();
      var lowerKeyword = keyword.toLowerCase();

      // Find all occurrences of the keyword
      var index = 0;
      while ((index = lowerFileName.indexOf(lowerKeyword, index)) !== -1) {
        var beforeChar = index === 0 ? '' : lowerFileName.charAt(index - 1);
        var afterChar = index + lowerKeyword.length >= lowerFileName.length ? '' : lowerFileName.charAt(index + lowerKeyword.length);

        // Check if keyword is bounded by non-alphabetic characters
        var beforeOk = beforeChar === '' || !/[a-z]/.test(beforeChar);
        var afterOk = afterChar === '' || !/[a-z]/.test(afterChar);

        if (beforeOk && afterOk) {
          return true;
        }
        index++;
      }
      return false;
    }

    // Function to find matching file for a layer pattern
    function findMatchingFile(layerPattern) {
      for (var i = 0; i < imageFiles.length; i++) {
        var fileName = imageFiles[i].name;

        for (var j = 0; j < layerPattern.patterns.length; j++) {
          var pattern = layerPattern.patterns[j];
          if (containsKeyword(fileName, pattern)) {
            return imageFiles[i];
          }
        }
      }
      return null;
    }

    var doc = null;
    var importedLayers = [];
    var skippedLayers = [];

    // Process each layer pattern in reverse order (bottom to top stacking)
    for (var i = 0; i < layerPatterns.length; i++) {
      var layerPattern = layerPatterns[i];
      var matchingFile = findMatchingFile(layerPattern);

      if (matchingFile) {
        try {
          var sourceDoc = app.open(matchingFile);
          if (sourceDoc.layers.length > 1) {
            sourceDoc.flatten();
          }

          // For first image, create document by duplicating from source (no background layer approach)
          if (!doc) {
            // Create new document by duplicating the source document (avoids Layer 0 entirely)
            doc = sourceDoc.duplicate("Narrowband Processing");

            // Close the source document
            sourceDoc.close(SaveOptions.DONOTSAVECHANGES);

            // Make the new document active
            app.activeDocument = doc;

            // Rename the layer to the correct name
            doc.activeLayer.name = layerPattern.name;

          } else {
            // For subsequent images, duplicate normally
            // Make source document active for duplication
            app.activeDocument = sourceDoc;
            sourceDoc.activeLayer.duplicate(doc);
            sourceDoc.close(SaveOptions.DONOTSAVECHANGES);

            // Switch back to target document and rename the layer
            app.activeDocument = doc;
            doc.activeLayer.name = layerPattern.name;
          }

          // Make Annotation layer truly transparent with alpha channel
          if (layerPattern.name === "Annotation") {
            // Ensure we have alpha channel for transparency
            try {
              // Add alpha channel if it doesn't exist
              if (doc.channels.length < 4) {
                doc.channels.add();
              }

              // Set layer to use transparency (blend mode and opacity)
              doc.activeLayer.opacity = 75; // 75% opacity for visibility
              doc.activeLayer.blendMode = BlendMode.NORMAL; // Normal blend for proper transparency

              // Enable transparency for the layer
              doc.activeLayer.fillOpacity = 75;
            } catch (e) {
              // Fallback to just opacity if alpha channel operations fail
              doc.activeLayer.opacity = 75;
            }
          }

          importedLayers.push(layerPattern.description + " <- " + matchingFile.name);

        } catch (e) {
          skippedLayers.push(layerPattern.description + " (Error: " + e.message + ")");
        }
      } else {
        skippedLayers.push(layerPattern.description + " (No matching file found)");
      }
    }

    // Show import summary
    var summary = "IMPORT SUMMARY:\n\n";
    summary += "IMPORTED (" + importedLayers.length + "):\n";
    for (var i = 0; i < importedLayers.length; i++) {
      summary += "* " + importedLayers[i] + "\n";
    }

    if (skippedLayers.length > 0) {
      summary += "\nSKIPPED (" + skippedLayers.length + "):\n";
      for (var i = 0; i < skippedLayers.length; i++) {
        summary += "* " + skippedLayers[i] + "\n";
      }
      summary += "\nNote: Groups for skipped layers will be automatically excluded from processing.";
    }

    alert(summary);

    return doc !== null; // Return true if at least one file was imported
  }

  // Import files first
  if (!importFilesInOrder()) {
    return;
  }

  // Check which channels are available
  var doc = app.activeDocument;
  var hasHydrogen = false;
  var hasOxygen = false;
  var hasSulfur = false;

  try { doc.artLayers.getByName("Starless_Ha"); hasHydrogen = true; } catch (e) {}
  try { doc.artLayers.getByName("Starless_OIII"); hasOxygen = true; } catch (e) {}
  try { doc.artLayers.getByName("Starless_SII"); hasSulfur = true; } catch (e) {}

  // Show palette selection dialog
  var selectedPalette = showPaletteDialog(hasHydrogen, hasOxygen, hasSulfur);
  if (!selectedPalette) {
    alert("Script cancelled by user.");
    return;
  }

  // Create simple narrowband groups - just create empty groups with proper names
  alert("Creating narrowband groups for your " + (hasHydrogen ? "Ha " : "") + (hasOxygen ? "OIII " : "") + (hasSulfur ? "SII " : "") + "layers...");
  
  createSimpleNarrowbandGroups(hasHydrogen, hasOxygen, hasSulfur);
  
  alert("SUCCESS! Narrowband groups created!\n\n* Groups created only for existing layers\n* Layers organized properly\n* Ready for " + selectedPalette + " processing!");
}

// Function to show palette selection dialog (from your working version)
function showPaletteDialog(hasH, hasO, hasS) {
  var dialog = new Window("dialog", "Narrowband Color Palette Selection");
  dialog.orientation = "column";
  dialog.alignChildren = "left";
  dialog.spacing = 10;
  dialog.margins = 20;

  // Title
  var titleGroup = dialog.add("group");
  titleGroup.add("statictext", undefined, "Select Color Palette for Narrowband Processing:");

  // Available channels info
  var channelGroup = dialog.add("group");
  channelGroup.orientation = "column";
  channelGroup.alignChildren = "left";
  channelGroup.add("statictext", undefined, "Available Channels:");
  var channelText = "";
  if (hasH) channelText += "* Hydrogen (Ha)  ";
  if (hasO) channelText += "* Oxygen (OIII)  ";
  if (hasS) channelText += "* Sulfur (SII)";
  channelGroup.add("statictext", undefined, channelText);

  // Palette options
  var paletteGroup = dialog.add("group");
  paletteGroup.orientation = "column";
  paletteGroup.alignChildren = "left";

  var radioButtons = [];

  // SHO Palette (only if all 3 channels present)
  if (hasH && hasO && hasS) {
    var shoRadio = paletteGroup.add("radiobutton", undefined, "SHO Palette (Standard)");
    shoRadio.value = true; // Default selection
    radioButtons.push({button: shoRadio, palette: "SHO"});

    var shoDetails = paletteGroup.add("group");
    shoDetails.orientation = "column";
    shoDetails.alignChildren = "left";
    shoDetails.margins = [20, 0, 0, 0];
    shoDetails.add("statictext", undefined, "  - S II -> Red (0 degrees)");
    shoDetails.add("statictext", undefined, "  - Ha -> Green (120 degrees)");
    shoDetails.add("statictext", undefined, "  - O III -> Blue (240 degrees)");

    paletteGroup.add("panel", undefined, "");

    // Golden SHO Palette
    var goldenRadio = paletteGroup.add("radiobutton", undefined, "Golden SHO Palette");
    radioButtons.push({button: goldenRadio, palette: "Golden"});

    var goldenDetails = paletteGroup.add("group");
    goldenDetails.orientation = "column";
    goldenDetails.alignChildren = "left";
    goldenDetails.margins = [20, 0, 0, 0];
    goldenDetails.add("statictext", undefined, "  - S II -> Orange-Red (30 degrees)");
    goldenDetails.add("statictext", undefined, "  - Ha -> Gold/Yellow (55 degrees)");
    goldenDetails.add("statictext", undefined, "  - O III -> Blue (240 degrees)");

    paletteGroup.add("panel", undefined, "");
  }

  // HOO Palette (if H and O present)
  if (hasH && hasO) {
    var hooRadio = paletteGroup.add("radiobutton", undefined, "HOO Palette");
    if (!hasS) hooRadio.value = true; // Default if no sulfur
    radioButtons.push({button: hooRadio, palette: "HOO"});

    var hooDetails = paletteGroup.add("group");
    hooDetails.orientation = "column";
    hooDetails.alignChildren = "left";
    hooDetails.margins = [20, 0, 0, 0];
    hooDetails.add("statictext", undefined, "  - Ha -> Red (0 degrees)");
    hooDetails.add("statictext", undefined, "  - O III -> Cyan (180 degrees)");
    if (hasS) {
      var grayedText = hooDetails.add("statictext", undefined, "  - S II -> (not used)");
      grayedText.enabled = false;
    }
  }

  // Buttons
  var buttonGroup = dialog.add("group");
  buttonGroup.alignment = "center";
  var okButton = buttonGroup.add("button", undefined, "OK");
  var cancelButton = buttonGroup.add("button", undefined, "Cancel");

  okButton.onClick = function() {
    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].button.value) {
        dialog.selectedPalette = radioButtons[i].palette;
        break;
      }
    }
    dialog.close();
  };

  cancelButton.onClick = function() {
    dialog.selectedPalette = null;
    dialog.close();
  };

  dialog.show();
  return dialog.selectedPalette;
}

// Simple function to create basic groups - this is what was missing!
function createSimpleNarrowbandGroups(hasH, hasO, hasS) {
  var doc = app.activeDocument;

  try {
    // Create groups only for existing channels
    if (hasS) {
      var sulfurGroup = doc.layerSets.add();
      sulfurGroup.name = "Sulfur";
      sulfurGroup.blendMode = BlendMode.SCREEN;
    }

    if (hasO) {
      var oxygenGroup = doc.layerSets.add();
      oxygenGroup.name = "Oxygen";
      oxygenGroup.blendMode = BlendMode.SCREEN;
    }

    if (hasH) {
      var hydrogenGroup = doc.layerSets.add();
      hydrogenGroup.name = "Hydrogen";
      hydrogenGroup.blendMode = BlendMode.SCREEN; // Hydrogen group must be screen mode
    }

    // Create Stars group if stars layers exist
    try {
      doc.artLayers.getByName("Stars_RGB_Combined");
      var starsGroup = doc.layerSets.add();
      starsGroup.name = "Stars";
      starsGroup.blendMode = BlendMode.SCREEN;
    } catch (e) {
      // No stars layer, skip
    }

    // Create Annotation group if annotation layer exists
    try {
      doc.artLayers.getByName("Annotation");
      var annotationGroup = doc.layerSets.add();
      annotationGroup.name = "Annotation";
      annotationGroup.blendMode = BlendMode.SCREEN;
    } catch (e) {
      // No annotation layer, skip
    }

  } catch (e) {
    alert("Error creating groups: " + e.message);
  }
}

// Main execution
WorkingNarrowbandProcessor();
